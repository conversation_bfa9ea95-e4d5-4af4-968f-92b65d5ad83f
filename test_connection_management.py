#!/usr/bin/env python3
"""
测试WebSocket连接管理的脚本
用于验证用户连接、断开连接和重复连接的处理是否正确
"""

import asyncio
import websockets
import json
import time
from datetime import datetime

async def test_user_connection(user_id: str, duration: int = 10):
    """
    测试单个用户的连接
    
    Args:
        user_id: 用户ID
        duration: 连接持续时间（秒）
    """
    uri = f"wss://chats.mfcad.com/api/chat/connect_chat?user_code={user_id}"
    
    try:
        print(f"[{datetime.now()}] 用户 {user_id} 开始连接...")
        
        async with websockets.connect(uri) as websocket:
            print(f"[{datetime.now()}] 用户 {user_id} 连接成功")
            
            # 发送心跳包
            heartbeat_data = {
                "type": "heartbeat",
                "timestamp": time.time()
            }
            await websocket.send(json.dumps(heartbeat_data))
            print(f"[{datetime.now()}] 用户 {user_id} 发送心跳包")
            
            # 等待指定时间
            await asyncio.sleep(duration)
            
            print(f"[{datetime.now()}] 用户 {user_id} 准备断开连接")
            
    except Exception as e:
        print(f"[{datetime.now()}] 用户 {user_id} 连接出错: {str(e)}")

async def test_duplicate_connections(user_id: str):
    """
    测试同一用户的重复连接
    
    Args:
        user_id: 用户ID
    """
    uri = f"wss://chats.mfcad.com/api/chat/connect_chat?user_code={user_id}"
    
    print(f"[{datetime.now()}] 开始测试用户 {user_id} 的重复连接...")
    
    # 第一个连接
    try:
        print(f"[{datetime.now()}] 建立第一个连接...")
        websocket1 = await websockets.connect(uri)
        print(f"[{datetime.now()}] 第一个连接建立成功")
        
        # 等待2秒
        await asyncio.sleep(2)
        
        # 第二个连接（应该会替换第一个）
        print(f"[{datetime.now()}] 建立第二个连接...")
        websocket2 = await websockets.connect(uri)
        print(f"[{datetime.now()}] 第二个连接建立成功")
        
        # 尝试在第一个连接上发送消息（应该失败）
        try:
            heartbeat_data = {
                "type": "heartbeat",
                "timestamp": time.time()
            }
            await websocket1.send(json.dumps(heartbeat_data))
            print(f"[{datetime.now()}] 第一个连接仍然可用（异常情况）")
        except Exception as e:
            print(f"[{datetime.now()}] 第一个连接已失效（正常情况）: {str(e)}")
        
        # 在第二个连接上发送消息
        try:
            heartbeat_data = {
                "type": "heartbeat",
                "timestamp": time.time()
            }
            await websocket2.send(json.dumps(heartbeat_data))
            print(f"[{datetime.now()}] 第二个连接正常工作")
        except Exception as e:
            print(f"[{datetime.now()}] 第二个连接出错: {str(e)}")
        
        # 等待5秒后关闭
        await asyncio.sleep(5)
        
        await websocket2.close()
        print(f"[{datetime.now()}] 第二个连接已关闭")
        
    except Exception as e:
        print(f"[{datetime.now()}] 重复连接测试出错: {str(e)}")

async def test_multiple_users():
    """
    测试多个用户同时连接
    """
    print(f"[{datetime.now()}] 开始测试多用户连接...")
    
    # 创建多个用户连接任务
    tasks = []
    user_ids = ["test_user_1", "test_user_2", "test_user_3"]
    
    for user_id in user_ids:
        task = asyncio.create_task(test_user_connection(user_id, 15))
        tasks.append(task)
    
    # 等待所有任务完成
    await asyncio.gather(*tasks)
    
    print(f"[{datetime.now()}] 多用户连接测试完成")

async def main():
    """
    主测试函数
    """
    print("=" * 50)
    print("WebSocket连接管理测试")
    print("=" * 50)
    
    # 测试1: 单用户连接
    print("\n1. 测试单用户连接...")
    await test_user_connection("single_user", 5)
    
    # 等待2秒
    await asyncio.sleep(2)
    
    # 测试2: 重复连接
    print("\n2. 测试重复连接...")
    await test_duplicate_connections("duplicate_user")
    
    # 等待2秒
    await asyncio.sleep(2)
    
    # 测试3: 多用户连接
    print("\n3. 测试多用户连接...")
    await test_multiple_users()
    
    print("\n" + "=" * 50)
    print("所有测试完成")
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(main())
